package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/precize/logger"
	"github.com/precize/pserver/server/weaviate/model"
	"github.com/precize/pserver/server/weaviate/service"
	"github.com/precize/pserver/server/weaviate/utils"
	"github.com/weaviate/weaviate/entities/models"
)

type WeaviateController struct {
	service *service.WeaviateService
}

func NewWeaviateController(service *service.WeaviateService) *WeaviateController {
	return &WeaviateController{service: service}
}

func (c *WeaviateController) CreateClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		utils.WriteError(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req model.CreateClassRequest
	if err := utils.ParseJSON(r, &req); err != nil {
		utils.WriteError(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if req.ClassName == "" {
		utils.WriteError(w, http.StatusBadRequest, "className is required")
		return
	}

	if len(req.VectorConfig) > 1 && len(req.Properties) == 0 {
		utils.WriteError(w, http.StatusBadRequest, "If vector configs are provided, properties are required")
		return
	}

	response, err := c.service.CreateClass(r.Context(), req)
	if err != nil {
		utils.WriteError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.WriteResponse(w, http.StatusOK, response)
}

func CreateClassV2(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req model.CreateClassRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if len(req.VectorConfig) > 1 && len(req.Properties) == 0 {
		http.Error(w, "If vector configs are provided, properties are required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()

	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	exists, err := weaviateClient.Schema().ClassExistenceChecker().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to check class existence: %v", err)
		http.Error(w, "Failed to check class existence", http.StatusInternalServerError)
		return
	}

	if exists {
		logger.Print(logger.INFO, "Class '%s' already exists, skipping creation", req.ClassName)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(Response{
			Success: true,
			Message: fmt.Sprintf("Class '%s' already exists, skipping creation", req.ClassName),
		})

		return
	}

	vectorConfig := make(map[string]models.VectorConfig)
	for vectorName, config := range req.VectorConfig {
		vectorConfig[vectorName] = models.VectorConfig{
			Vectorizer:        config.Vectorizer,
			VectorIndexType:   config.VectorIndexType,
			VectorIndexConfig: config.VectorIndexConfig,
		}
	}

	class := &models.Class{
		Class:        req.ClassName,
		Description:  req.Description,
		VectorConfig: vectorConfig,
	}

	if len(req.Properties) > 0 {
		var properties []*models.Property
		for _, prop := range req.Properties {
			properties = append(properties, &models.Property{
				Name:     prop.Name,
				DataType: prop.DataType,
			})
		}

		class.Properties = properties
	}

	err = weaviateClient.Schema().ClassCreator().WithClass(class).Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to create class in weaviate: %v", err)
		http.Error(w, "Failed to create class", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully created class '%s'", req.ClassName),
	})
}
