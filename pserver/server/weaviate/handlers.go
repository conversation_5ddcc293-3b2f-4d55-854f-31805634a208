package weaviate

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"slices"

	"github.com/precize/common"
	"github.com/precize/logger"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/graphql"
	"github.com/weaviate/weaviate/entities/models"
)

func InsertData(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req InsertDataRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}
	if len(req.Objects) == 0 {
		http.Error(w, "objects list cannot be empty", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	batcher := weaviateClient.Batch().ObjectsBatcher()
	for _, obj := range req.Objects {
		batcher = batcher.WithObjects(&models.Object{
			Class:      req.ClassName,
			Properties: obj,
		})
	}

	batchRes, err := batcher.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch insert: %v", err)
		http.Error(w, "Failed to insert data", http.StatusInternalServerError)
		return
	}

	var errorMsgs []string
	for _, res := range batchRes {
		if res.Result.Errors != nil {
			for _, batchErr := range res.Result.Errors.Error {
				if batchErr != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%v", *batchErr))
				}
			}
		}
	}

	if len(errorMsgs) > 0 {
		logger.Print(logger.ERROR, "Some objects failed to insert: %v", errorMsgs)
		http.Error(w, fmt.Sprintf("Some objects failed to insert: %v", errorMsgs), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully inserted %d objects into class '%s'", len(req.Objects), req.ClassName),
	})
}

func SearchSimilar(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req SearchSimilarRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if req.Query == "" {
		http.Error(w, "query is required", http.StatusBadRequest)
		return
	}

	if len(req.Fields) == 0 {
		http.Error(w, "fields list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.SearchType == "" {
		req.SearchType = "hybrid"
	}

	validSearchTypes := []string{"bm25", "vector", "hybrid"}
	isValidSearchType := slices.Contains(validSearchTypes, req.SearchType)
	if !isValidSearchType {
		http.Error(w, "searchType must be one of: bm25, vector, hybrid", http.StatusBadRequest)
		return
	}

	if req.Regulator == nil {
		switch req.SearchType {
		case "hybrid":
			req.Regulator = common.Float32Ptr(1.0)
		case "vector":
		}
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	gqlBuilder := weaviateClient.GraphQL().Get().
		WithClassName(req.ClassName).
		WithLimit(req.Limit)

	fields := []graphql.Field{}
	for _, fieldName := range req.Fields {
		fields = append(fields, graphql.Field{Name: fieldName})
	}

	switch req.SearchType {
	case "bm25":
		bm25Builder := (&graphql.BM25ArgumentBuilder{}).WithQuery(req.Query)
		if len(req.Properties) > 0 {
			bm25Builder = bm25Builder.WithProperties(req.Properties...)
		}
		gqlBuilder = gqlBuilder.WithBM25(bm25Builder)

		if req.Regulator != nil {
			gqlBuilder = gqlBuilder.WithAutocut(req.AutoCut)
		}

		fields = append(fields, graphql.Field{Name: "_additional",
			Fields: []graphql.Field{
				{Name: "score"},
			}})

	case "vector":
		nearTextBuilder := weaviateClient.GraphQL().NearTextArgBuilder().WithConcepts([]string{req.Query})
		if req.Regulator != nil {
			nearTextBuilder = nearTextBuilder.WithDistance(*req.Regulator)
		}
		gqlBuilder = gqlBuilder.WithNearText(nearTextBuilder)

		if req.Regulator != nil {
			gqlBuilder = gqlBuilder.WithAutocut(req.AutoCut)
		}

		fields = append(fields, graphql.Field{Name: "_additional",
			Fields: []graphql.Field{
				{Name: "distance"},
			}})

	case "hybrid":
		hybridBuilder := weaviateClient.GraphQL().HybridArgumentBuilder().WithQuery(req.Query)
		if req.Regulator != nil {
			hybridBuilder = hybridBuilder.WithAlpha(*req.Regulator)
		}

		if req.MaxVectorDistance != nil {
			hybridBuilder = hybridBuilder.WithMaxVectorDistance(*req.MaxVectorDistance)
		}

		if len(req.Properties) > 0 {
			hybridBuilder = hybridBuilder.WithProperties(req.Properties)
		}

		if len(req.FusionType) > 0 {
			hybridBuilder = hybridBuilder.WithFusionType(graphql.FusionType(req.FusionType))
		}

		gqlBuilder = gqlBuilder.WithHybrid(hybridBuilder)

		if req.Regulator != nil {
			gqlBuilder = gqlBuilder.WithAutocut(req.AutoCut)
		}

		fields = append(fields, graphql.Field{Name: "_additional",
			Fields: []graphql.Field{
				{Name: "score"}, {Name: "explainScore"},
			}})
	}

	gqlBuilder = gqlBuilder.WithFields(fields...)

	for _, filter := range req.Filters {
		whereFilter := buildWhereFilter(filter)
		gqlBuilder = gqlBuilder.WithWhere(whereFilter)
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Search failed: %v", err)
		http.Error(w, "Search failed", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully searched class '%s'", req.ClassName),
		Data:    result,
	})
}

func DeleteByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req DeleteByIDRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if req.ID == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	err = weaviateClient.Data().Deleter().
		WithClassName(req.ClassName).
		WithID(req.ID).
		Do(ctx)

	if err != nil {
		logger.Print(logger.ERROR, "Failed to delete object: %v", err)
		http.Error(w, "Failed to delete object", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted object with ID '%s' from class '%s'", req.ID, req.ClassName),
	})
}

func DeleteByQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req DeleteByQueryRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if len(req.Filters) == 0 {
		http.Error(w, "filters list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.Output == "" {
		req.Output = "minimal"
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	batchDeleter := weaviateClient.Batch().ObjectsBatchDeleter().
		WithClassName(req.ClassName).
		WithOutput(req.Output)

	for _, filter := range req.Filters {
		whereFilter := buildWhereFilter(filter)
		batchDeleter = batchDeleter.WithWhere(whereFilter)
	}

	response, err := batchDeleter.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to execute batch delete: %v", err)
		http.Error(w, "Failed to execute batch delete", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully executed batch delete on class '%s'", req.ClassName),
		Data:    response,
	})
}

func DeleteClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req DeleteClassRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	err = weaviateClient.Schema().ClassDeleter().WithClassName(req.ClassName).Do(ctx)
	if err != nil {
		if strings.Contains(err.Error(), "400") || strings.Contains(strings.ToLower(err.Error()), "not found") {
			logger.Print(logger.INFO, "Class '%s' does not exist, deletion skipped", req.ClassName)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(Response{
				Success: true,
				Message: fmt.Sprintf("Class '%s' does not exist, deletion skipped", req.ClassName),
			})
			return
		}

		logger.Print(logger.ERROR, "Failed to delete class: %v", err)
		http.Error(w, "Failed to delete class", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully deleted class '%s'", req.ClassName),
	})
}

func GetClass(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req GetClassRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	class, err := weaviateClient.Schema().ClassGetter().
		WithClassName(req.ClassName).
		Do(ctx)

	if err != nil {
		// Check if it's a 404 error (class doesn't exist)
		if strings.Contains(err.Error(), "404") || strings.Contains(strings.ToLower(err.Error()), "not found") {
			logger.Print(logger.INFO, "Class '%s' not found", req.ClassName)
			http.Error(w, fmt.Sprintf("Class '%s' not found", req.ClassName), http.StatusNotFound)
			return
		}

		logger.Print(logger.ERROR, "Failed to get class: %v", err)
		http.Error(w, "Failed to get class", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved class '%s'", req.ClassName),
		Data:    class,
	})
}

func SearchAll(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req SearchAllRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if len(req.Fields) == 0 {
		http.Error(w, "fields list cannot be empty", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	fields := []graphql.Field{}
	for _, fieldName := range req.Fields {
		fields = append(fields, graphql.Field{Name: fieldName})
	}

	additionalFields := "_additional { id"
	if req.IncludeVector {
		additionalFields += " vector"
	}
	additionalFields += " }"
	fields = append(fields, graphql.Field{Name: additionalFields})

	gqlBuilder := weaviateClient.GraphQL().Get().
		WithClassName(req.ClassName).
		WithFields(fields...)

	if req.Limit > 0 {
		gqlBuilder = gqlBuilder.WithLimit(req.Limit)
	}

	if req.Cursor != "" {
		gqlBuilder = gqlBuilder.WithAfter(req.Cursor)
	}

	for _, filter := range req.Filters {
		whereFilter := buildWhereFilter(filter)
		gqlBuilder = gqlBuilder.WithWhere(whereFilter)
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "SearchAll failed: %v", err)
		http.Error(w, "SearchAll failed", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved data from class '%s'", req.ClassName),
		Data:    result,
	})
}

func SearchByID(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req SearchByIDRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if req.ID == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	objects, err := weaviateClient.Data().ObjectsGetter().
		WithClassName(req.ClassName).
		WithID(req.ID).
		WithVector().Do(ctx)

	if err != nil {
		if strings.Contains(err.Error(), "404") || strings.Contains(strings.ToLower(err.Error()), "not found") {
			logger.Print(logger.INFO, "Object with ID '%s' not found in class '%s'", req.ID, req.ClassName)
			http.Error(w, fmt.Sprintf("Object with ID '%s' not found", req.ID), http.StatusNotFound)
			return
		}

		logger.Print(logger.ERROR, "Failed to get object by ID: %v", err)
		http.Error(w, "Failed to get object by ID", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully retrieved object with ID '%s' from class '%s'", req.ID, req.ClassName),
		Data:    objects,
	})
}

func SearchQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	var req SearchQueryRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if req.ClassName == "" {
		http.Error(w, "className is required", http.StatusBadRequest)
		return
	}

	if len(req.Fields) == 0 {
		http.Error(w, "fields list cannot be empty", http.StatusBadRequest)
		return
	}

	if len(req.Filters) == 0 {
		http.Error(w, "filters list cannot be empty", http.StatusBadRequest)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	ctx := context.Background()
	if weaviateClient == nil {
		logger.Print(logger.ERROR, "Weaviate client not initialized")
		http.Error(w, "Weaviate client not initialized", http.StatusInternalServerError)
		return
	}

	gqlBuilder := weaviateClient.GraphQL().Get().
		WithClassName(req.ClassName).
		WithLimit(req.Limit)

	fields := []graphql.Field{}
	for _, fieldName := range req.Fields {
		fields = append(fields, graphql.Field{Name: fieldName})
	}

	gqlBuilder = gqlBuilder.WithFields(fields...)

	for _, filter := range req.Filters {
		whereFilter := buildWhereFilter(filter)
		gqlBuilder = gqlBuilder.WithWhere(whereFilter)
	}

	result, err := gqlBuilder.Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Query search failed: %v", err)
		http.Error(w, "Query search failed", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Success: true,
		Message: fmt.Sprintf("Successfully queried class '%s' with filters", req.ClassName),
		Data:    result,
	})
}
