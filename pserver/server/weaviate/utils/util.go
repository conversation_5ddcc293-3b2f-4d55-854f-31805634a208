package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/precize/logger"
	"github.com/precize/pserver/server/weaviate/model"
)

func ParseJSON(r *http.Request, v interface{}) error {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		return fmt.Errorf("failed to read request body: %w", err)
	}

	if err := json.Unmarshal(body, v); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		return fmt.Errorf("invalid JSON format: %w", err)
	}
	return nil
}

// WriteResponse writes a JSON response to the HTTP response writer.
func WriteResponse(w http.ResponseWriter, status int, response model.Response) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Print(logger.ERROR, "Failed to write response: %v", err)
	}
}

func WriteError(w http.ResponseWriter, status int, message string) {
	WriteResponse(w, status, model.Response{
		Success: false,
		Message: message,
	})
}
