package weaviate

import (
	"context"
	"encoding/base64"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/weaviate/weaviate-go-client/v5/weaviate"
)

var weaviateClient *weaviate.Client

func InitializeWeaviateClient() error {
	logger.Print(logger.INFO, "Initializing Weaviate client...")

	host := config.AppConfig.Weaviate.Host
	if host == "" {
		host = "localhost:8080"
	}
	scheme := config.AppConfig.Weaviate.Scheme
	if scheme == "" {
		scheme = "http"
	}

	openAIAPIKey := ""

	decodedAPIKey, err := base64.StdEncoding.DecodeString(config.AppConfig.Weaviate.OpenAIAPIKey)
	if err == nil {
		decryptedKey, err := common.DecryptTextAES(decodedAPIKey)
		if err == nil {
			openAIAPIKey = string(decryptedKey)
		} else {
			openAIAPIKey = config.AppConfig.Weaviate.OpenAIAPIKey
		}
	} else {
		logger.Print(logger.ERROR, "Failed to decode base64", err)
		openAIAPIKey = config.AppConfig.Weaviate.OpenAIAPIKey
	}

	cfg := weaviate.Config{
		Host:   host,
		Scheme: scheme,
		Headers: map[string]string{
			"X-OpenAI-Api-Key": openAIAPIKey,
		},
	}

	client, err := weaviate.NewClient(cfg)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to init Weaviate client: %v", err)
		return err
	}

	ctx := context.Background()
	live, err := client.Misc().LiveChecker().Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Weaviate not reachable: %v", err)
		return err
	}

	logger.Print(logger.INFO, "Weaviate connection status: ", live)
	weaviateClient = client
	return nil
}
