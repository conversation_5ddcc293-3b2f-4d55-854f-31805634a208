2025/09/11 09:31:32 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 09:31:33 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "precize-es-cluster",
  "cluster_uuid" : "QgP2-KFsSSmtQYydNJj5nA",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 09:31:33 Index iac_git_commits exists - 
2025/09/11 09:31:34 Index cfstack_templates exists - 
2025/09/11 09:31:34 Index arm_templates exists - 
2025/09/11 09:31:34 Index terraform_resources exists - 
2025/09/11 09:31:35 Index tf_commits exists - 
2025/09/11 09:31:36 Index tf_variables exists - 
2025/09/11 09:31:36 Index resource_context exists - 
2025/09/11 09:31:36 Index text_lookup exists - 
2025/09/11 09:31:37 Index ai_resources exists - 
2025/09/11 09:31:37 Index idp_events exists - 
2025/09/11 09:31:37 Index idp_users exists - 
2025/09/11 09:31:38 Index idp_apps exists - 
2025/09/11 09:31:38 Index idp_groups exists - 
2025/09/11 09:31:38 Index cloud_incidents exists - 
2025/09/11 09:31:39 Index jira_issues exists - 
2025/09/11 09:31:39 Index jira_data exists - 
2025/09/11 09:31:39 Index jira_resources exists - 
2025/09/11 09:31:40 Index precize_creations exists - 
2025/09/11 09:31:40 Index external_cloud_resources exists - 
2025/09/11 09:31:40 Initializing tenant providers - 
2025/09/11 09:31:48 Initialized tenant providers - 
2025/09/11 09:31:48 Initializing generic constants - 
2025/09/11 09:31:49 Initialized generic constants - 
2025/09/11 09:31:49 Starting pre-commit cronjob - 
2025/09/11 09:31:49 Finished pre-commit cronjob - 
2025/09/11 09:31:49 [46] [LdjlmogBQRu6dsYIL82Z] Processing tenant - 
2025/09/11 09:31:49 [46] Calling cred api for - LdjlmogBQRu6dsYIL82Z - 
2025/09/11 09:32:21 [46] [LdjlmogBQRu6dsYIL82Z] Starting Wiz Cloud Resources collection from 2025-09-08T13:05:41.860Z to 2025-09-11T04:02:13.843Z - 1757563333843 - 
2025/09/11 09:32:35 [46] [LdjlmogBQRu6dsYIL82Z] Starting Wiz cloud resources collection - 
2025/09/11 09:33:26 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 09:33:27 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "precize-es-cluster",
  "cluster_uuid" : "QgP2-KFsSSmtQYydNJj5nA",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 09:33:28 Index iac_git_commits exists - 
2025/09/11 09:33:28 Index cfstack_templates exists - 
2025/09/11 09:33:28 Index arm_templates exists - 
2025/09/11 09:33:29 Index terraform_resources exists - 
2025/09/11 09:33:29 Index tf_commits exists - 
2025/09/11 09:33:29 Index tf_variables exists - 
2025/09/11 09:33:30 Index resource_context exists - 
2025/09/11 09:33:30 Index text_lookup exists - 
2025/09/11 09:33:30 Index ai_resources exists - 
2025/09/11 09:33:31 Index idp_events exists - 
2025/09/11 09:33:31 Index idp_users exists - 
2025/09/11 09:33:31 Index idp_apps exists - 
2025/09/11 09:33:32 Index idp_groups exists - 
2025/09/11 09:33:32 Index cloud_incidents exists - 
2025/09/11 09:33:33 Index jira_issues exists - 
2025/09/11 09:33:33 Index jira_data exists - 
2025/09/11 09:33:33 Index jira_resources exists - 
2025/09/11 09:33:34 Index precize_creations exists - 
2025/09/11 09:33:34 Index external_cloud_resources exists - 
2025/09/11 09:33:34 Initializing tenant providers - 
2025/09/11 09:33:43 Initialized tenant providers - 
2025/09/11 09:33:43 Initializing generic constants - 
2025/09/11 09:33:44 Initialized generic constants - 
2025/09/11 09:33:44 Starting pre-commit cronjob - 
2025/09/11 09:33:44 Finished pre-commit cronjob - 
2025/09/11 09:33:45 [6] [LdjlmogBQRu6dsYIL82Z] Processing tenant - 
2025/09/11 09:33:45 [6] Calling cred api for - LdjlmogBQRu6dsYIL82Z - 
2025/09/11 09:33:59 [6] [LdjlmogBQRu6dsYIL82Z] Starting Wiz Cloud Resources collection from 0001-01-01T00:00:00.000Z to 2025-09-11T04:03:46.401Z - 1757563426401 - 
2025/09/11 09:34:01 [6] [LdjlmogBQRu6dsYIL82Z] Starting Wiz cloud resources collection - 
